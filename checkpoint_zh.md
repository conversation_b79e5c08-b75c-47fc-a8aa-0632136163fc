<!--Copyright 2024 The HuggingFace Team. All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
the License. You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.

⚠️ 注意：此文件为Markdown格式，但包含我们文档构建器的特定语法（类似于MDX），在您的Markdown查看器中可能无法正确渲染。

-->

# PEFT检查点格式

本文档描述了PEFT检查点文件的结构以及如何在PEFT格式和其他格式之间进行转换。

## PEFT文件

PEFT（参数高效微调）方法只更新模型参数的一小部分，而不是全部参数。这很好，因为检查点文件通常比原始模型文件小得多，更容易存储和共享。但是，这也意味着要加载PEFT模型，您还需要有原始模型可用。

当您在PEFT模型上调用[`~PeftModel.save_pretrained`]时，PEFT模型会保存三个文件，如下所述：

1. `adapter_model.safetensors` 或 `adapter_model.bin`

默认情况下，模型以`safetensors`格式保存，这是`bin`格式的安全替代方案，已知`bin`格式容易受到[安全漏洞](https://huggingface.co/docs/hub/security-pickle)的影响，因为它在底层使用pickle工具。不过，两种格式存储相同的`state_dict`，并且可以互换。

`state_dict`只包含适配器模块的参数，不包含基础模型。为了说明大小差异，正常的BERT模型需要约420MB的磁盘空间，而在此BERT模型之上的IA³适配器只需要约260KB。

2. `adapter_config.json`

`adapter_config.json`文件包含适配器模块的配置，这是加载模型所必需的。以下是应用于BERT模型的IA³适配器的`adapter_config.json`示例，使用标准设置：

```json
{
  "auto_mapping": {
    "base_model_class": "BertModel",
    "parent_library": "transformers.models.bert.modeling_bert"
  },
  "base_model_name_or_path": "bert-base-uncased",
  "fan_in_fan_out": false,
  "feedforward_modules": [
    "output.dense"
  ],
  "inference_mode": true,
  "init_ia3_weights": true,
  "modules_to_save": null,
  "peft_type": "IA3",
  "revision": null,
  "target_modules": [
    "key",
    "value",
    "output.dense"
  ],
  "task_type": null
}
```

配置文件包含：

- 存储的适配器模块类型，`"peft_type": "IA3"`
- 关于基础模型的信息，如`"base_model_name_or_path": "bert-base-uncased"`
- 模型的修订版本（如果有），`"revision": null`

如果基础模型不是预训练的Transformers模型，后两个条目将为`null`。除此之外，设置都与用于微调模型的特定IA³适配器相关。

3. `README.md`

生成的`README.md`是PEFT模型的模型卡，包含一些预填充的条目。其目的是使与他人共享模型变得更容易，并提供有关模型的一些基本信息。加载模型时不需要此文件。

## 转换为PEFT格式

从其他格式转换为PEFT格式时，我们需要`adapter_model.safetensors`（或`adapter_model.bin`）文件和`adapter_config.json`文件。

### adapter_model

对于模型权重，重要的是使用正确的参数名称到值的映射，以便PEFT加载文件。正确获得此映射需要检查实现细节，因为PEFT适配器没有普遍认可的格式。

幸运的是，对于常见的基本情况，弄清楚这个映射并不太复杂。让我们看一个具体的例子，[`LoraLayer`](https://github.com/huggingface/peft/blob/main/src/peft/tuners/lora/layer.py)：

```python
# 仅显示部分代码

class LoraLayer(BaseTunerLayer):
    # 可能包含（可训练）适配器权重的所有层名称
    adapter_layer_names = ("lora_A", "lora_B", "lora_embedding_A", "lora_embedding_B")
    # 可能包含适配器相关参数的其他参数名称
    other_param_names = ("r", "lora_alpha", "scaling", "lora_dropout")

    def __init__(self, base_layer: nn.Module, **kwargs) -> None:
        self.base_layer = base_layer
        self.r = {}
        self.lora_alpha = {}
        self.scaling = {}
        self.lora_dropout = nn.ModuleDict({})
        self.lora_A = nn.ModuleDict({})
        self.lora_B = nn.ModuleDict({})
        # 用于嵌入层
        self.lora_embedding_A = nn.ParameterDict({})
        self.lora_embedding_B = nn.ParameterDict({})
        # 将权重标记为未合并
        self._disable_adapters = False
        self.merged_adapters = []
        self.use_dora: dict[str, bool] = {}
        self.lora_magnitude_vector: Optional[torch.nn.ParameterDict] = None  # 用于DoRA
        self._caches: dict[str, Any] = {}
        self.kwargs = kwargs
```

在PEFT中所有`LoraLayer`类使用的`__init__`代码中，有一堆用于初始化模型的参数，但只有少数与检查点文件相关：`lora_A`、`lora_B`、`lora_embedding_A`和`lora_embedding_B`。这些参数在类属性`adapter_layer_names`中列出，包含可学习参数，因此必须包含在检查点文件中。所有其他参数，如秩`r`，都从`adapter_config.json`派生，必须包含在那里（除非使用默认值）。

让我们检查应用于BERT的PEFT LoRA模型的`state_dict`。使用默认LoRA设置打印前五个键时（其余键相同，只是层号不同），我们得到：

- `base_model.model.encoder.layer.0.attention.self.query.lora_A.weight` 
- `base_model.model.encoder.layer.0.attention.self.query.lora_B.weight` 
- `base_model.model.encoder.layer.0.attention.self.value.lora_A.weight` 
- `base_model.model.encoder.layer.0.attention.self.value.lora_B.weight` 
- `base_model.model.encoder.layer.1.attention.self.query.lora_A.weight`
- 等等

让我们分解一下：

- 默认情况下，对于BERT模型，LoRA应用于注意力模块的`query`和`value`层。这就是为什么您在每层的键名中看到`attention.self.query`和`attention.self.value`。
- LoRA将权重分解为两个低秩矩阵，`lora_A`和`lora_B`。这就是键名中`lora_A`和`lora_B`的来源。
- 这些LoRA矩阵实现为`nn.Linear`层，因此参数存储在`.weight`属性中（`lora_A.weight`、`lora_B.weight`）。
- 默认情况下，LoRA不应用于BERT的嵌入层，因此`lora_A_embedding`和`lora_B_embedding`_没有条目_。
- `state_dict`的键总是以`"base_model.model."`开头。原因是，在PEFT中，我们将基础模型包装在特定于调谐器的模型（在这种情况下为`LoraModel`）中，该模型本身包装在通用PEFT模型（`PeftModel`）中。因此，这两个前缀被添加到键中。转换为PEFT格式时，需要添加这些前缀。

<Tip>

对于前缀调谐技术（如提示调谐），最后一点不成立。在那里，额外的嵌入直接存储在`state_dict`中，键中没有添加任何前缀。

</Tip>

检查加载模型中的参数名称时，您可能会惊讶地发现它们看起来有点不同，例如`base_model.model.encoder.layer.0.attention.self.query.lora_A.default.weight`。区别在于倒数第二段中的*`.default`*部分。这部分存在是因为PEFT通常允许同时添加多个适配器（使用`nn.ModuleDict`或`nn.ParameterDict`来存储它们）。例如，如果您添加另一个名为"other"的适配器，该适配器的键将是`base_model.model.encoder.layer.0.attention.self.query.lora_A.other.weight`。

当您调用[`~PeftModel.save_pretrained`]时，适配器名称从键中剥离。原因是适配器名称不是模型架构的重要部分；它只是一个任意名称。加载适配器时，您可以选择完全不同的名称，模型仍然以相同的方式工作。这就是为什么适配器名称不存储在检查点文件中。

<Tip>

如果您调用`save_pretrained("some/path")`且适配器名称不是`"default"`，适配器将存储在与适配器同名的子目录中。因此，如果名称是"other"，它将存储在`some/path/other`内。

</Tip>

在某些情况下，决定将哪些值添加到检查点文件可能会变得更加复杂。例如，在PEFT中，DoRA实现为LoRA的特殊情况。如果您想将DoRA模型转换为PEFT，您应该创建一个带有DoRA额外条目的LoRA检查点。您可以在之前`LoraLayer`代码的`__init__`中看到这一点：

```python
self.lora_magnitude_vector: Optional[torch.nn.ParameterDict] = None  # 用于DoRA
```

这表明DoRA每层有一个可选的额外参数。

### adapter_config

加载PEFT模型所需的所有其他信息都包含在`adapter_config.json`文件中。让我们检查应用于BERT的LoRA模型的此文件：

```json
{
  "alpha_pattern": {},
  "auto_mapping": {
    "base_model_class": "BertModel",
    "parent_library": "transformers.models.bert.modeling_bert"
  },
  "base_model_name_or_path": "bert-base-uncased",
  "bias": "none",
  "fan_in_fan_out": false,
  "inference_mode": true,
  "init_lora_weights": true,
  "layer_replication": null,
  "layers_pattern": null,
  "layers_to_transform": null,
  "loftq_config": {},
  "lora_alpha": 8,
  "lora_dropout": 0.0,
  "megatron_config": null,
  "megatron_core": "megatron.core",
  "modules_to_save": null,
  "peft_type": "LORA",
  "r": 8,
  "rank_pattern": {},
  "revision": null,
  "target_modules": [
    "query",
    "value"
  ],
  "task_type": null,
  "use_dora": false,
  "use_rslora": false
}
```

这包含很多条目，乍一看，弄清楚所有正确的值可能会让人感到不知所措。但是，大多数条目对于加载模型并不是必需的。这要么是因为它们使用默认值且不需要添加，要么是因为它们只影响LoRA权重的初始化，这在加载模型时无关紧要。如果您发现不知道特定参数的作用，例如`"use_rslora"`，不要添加它，您应该没问题。还要注意，随着更多选项的添加，此文件将来会有更多条目，但应该向后兼容。

至少，您应该包含以下条目：

```json
{
  "target_modules": ["query", "value"],
  "peft_type": "LORA"
}
```

但是，建议添加尽可能多的条目，如秩`r`或`base_model_name_or_path`（如果是Transformers模型）。此信息可以帮助其他人更好地理解模型并更容易地共享它。要检查期望的键和值，请查看PEFT源代码中的[config.py](https://github.com/huggingface/peft/blob/main/src/peft/tuners/lora/config.py)文件（作为示例，这是LoRA的配置文件）。

## 模型存储

在某些情况下，您可能希望存储整个PEFT模型，包括基础权重。例如，如果尝试加载PEFT模型的用户无法获得基础模型，这可能是必要的。您可以先合并权重或将其转换为Transformer模型。

### 合并权重

存储整个PEFT模型最直接的方法是将适配器权重合并到基础权重中：

```python
merged_model = model.merge_and_unload()
merged_model.save_pretrained(...)
```

不过，这种方法有一些缺点：

- 一旦调用[`~LoraModel.merge_and_unload`]，您就会得到一个没有任何PEFT特定功能的基本模型。这意味着您不能再使用任何PEFT特定方法。
- 您无法取消合并权重、同时加载多个适配器、禁用适配器等。
- 并非所有PEFT方法都支持合并权重。
- 某些PEFT方法可能通常允许合并，但不适用于特定设置（例如，使用某些量化技术时）。
- 整个模型将比PEFT模型大得多，因为它也将包含所有基础权重。

但是，使用合并模型进行推理应该会稍微快一些。

### 转换为Transformers模型

保存整个模型的另一种方法，假设基础模型是Transformers模型，是使用这种巧妙的方法直接将PEFT权重插入基础模型并保存它，这只有在您"欺骗"Transformers相信PEFT模型不是PEFT模型时才有效。这只适用于LoRA，因为其他适配器未在Transformers中实现。

```python
model = ...  # PEFT模型
...
# 完成模型训练后，将其保存在临时位置
model.save_pretrained(<temp_location>)
# 现在直接将此模型加载到transformers模型中，不使用PEFT包装器
# PEFT权重直接注入到基础模型中
model_loaded = AutoModel.from_pretrained(<temp_location>)
# 现在让加载的模型相信它_不是_PEFT模型
model_loaded._hf_peft_config_loaded = False
# 现在当我们保存它时，它将保存整个模型
model_loaded.save_pretrained(<final_location>)
# 或上传到Hugging Face Hub
model_loaded.push_to_hub(<final_location>)
```

## 实际项目应用示例

基于本项目的实际代码，以下展示了PEFT检查点格式在实际应用中的具体实现。

### 项目中的PEFT实现

#### LoRA配置示例

基于 `hello_world.py` 的实现：

<augment_code_snippet path="Desktop/HelloWorld/hello_world.py" mode="EXCERPT">
````python
from transformers import AutoModelForCausalLM
from peft import LoraConfig, TaskType, get_peft_model

model = AutoModelForCausalLM.from_pretrained('facebook/opt-350m')

lora_config = LoraConfig(
    r=16,
    target_modules=["q_proj", "v_proj"],
    task_type=TaskType.CAUSAL_LM,
    lora_alpha=32,
    lora_dropout=0.05
)

lora_model = get_peft_model(model, lora_config)
lora_model.print_trainable_parameters()
````
</augment_code_snippet>

这个配置会生成以下格式的 `adapter_config.json`：

```json
{
  "alpha_pattern": {},
  "auto_mapping": null,
  "base_model_name_or_path": "facebook/opt-350m",
  "bias": "none",
  "fan_in_fan_out": false,
  "inference_mode": false,
  "init_lora_weights": true,
  "layers_pattern": null,
  "layers_to_transform": null,
  "lora_alpha": 32,
  "lora_dropout": 0.05,
  "modules_to_save": null,
  "peft_type": "LORA",
  "r": 16,
  "rank_pattern": {},
  "revision": null,
  "target_modules": [
    "q_proj",
    "v_proj"
  ],
  "task_type": "CAUSAL_LM",
  "use_dora": false,
  "use_rslora": false
}
```

#### P-Tuning配置示例

基于 `test.py` 和 `deepSeek.py` 的实现：

<augment_code_snippet path="Desktop/HelloWorld/test.py" mode="EXCERPT">
````python
from peft import PromptEncoderConfig, get_peft_model

# Apply PEFT configuration
peft_config = PromptEncoderConfig(task_type="CAUSAL_LM", num_virtual_tokens=20, encoder_hidden_size=128)
model = get_peft_model(model, peft_config)
model.print_trainable_parameters()
````
</augment_code_snippet>

这个配置会生成以下格式的 `adapter_config.json`：

```json
{
  "auto_mapping": null,
  "base_model_name_or_path": "bigscience/bloomz-560m",
  "encoder_dropout": 0.0,
  "encoder_hidden_size": 128,
  "encoder_num_layers": 2,
  "encoder_reparameterization_type": "MLP",
  "inference_mode": false,
  "num_attention_heads": 12,
  "num_layers": null,
  "num_transformer_submodules": null,
  "num_virtual_tokens": 20,
  "peft_type": "P_TUNING",
  "revision": null,
  "task_type": "CAUSAL_LM",
  "token_dim": null
}
```

### 检查点保存与加载实现

#### 完整的检查点保存

基于 `checkpoint.py` 的实现：

<augment_code_snippet path="Desktop/HelloWorld/checkpoint.py" mode="EXCERPT">
````python
def save_training_state(checkpoint_dir, epoch, best_eval_loss, optimizer, lr_scheduler):
    """保存完整的训练状态，包括模型、优化器和调度器状态"""
    training_state = {
        "epoch": epoch,
        "best_eval_loss": best_eval_loss,
        "optimizer_state_dict": optimizer.state_dict(),
        "lr_scheduler_state_dict": lr_scheduler.state_dict(),
    }

    # 保存JSON格式的基本信息
    state_path = os.path.join(checkpoint_dir, "training_state.json")
    with open(state_path, "w") as f:
        json_state = {k: v for k, v in training_state.items()
                     if k not in ["optimizer_state_dict", "lr_scheduler_state_dict"]}
        json.dump(json_state, f, indent=2)

    # 保存PyTorch tensor数据
    torch.save({
        "optimizer_state_dict": training_state["optimizer_state_dict"],
        "lr_scheduler_state_dict": training_state["lr_scheduler_state_dict"],
    }, os.path.join(checkpoint_dir, "training_state.pt"))
````
</augment_code_snippet>

#### 基础模型信息保存

<augment_code_snippet path="Desktop/HelloWorld/checkpoint.py" mode="EXCERPT">
````python
# Save base model info
base_model_info = {
    "base_model_name": base_model_name,
    "base_model_path": save_dir if save_base_model_copy else base_model_name
}
with open(os.path.join(checkpoint_dir, "base_model_info.json"), "w") as f:
    json.dump(base_model_info, f, indent=2)
````
</augment_code_snippet>

这会创建一个 `base_model_info.json` 文件：

```json
{
  "base_model_name": "bigscience/bloomz-560m",
  "base_model_path": "./fine_tuned_bloomz_560m"
}
```

#### 检查点加载实现

基于 `load_model.py` 的实现：

<augment_code_snippet path="Desktop/HelloWorld/load_model.py" mode="EXCERPT">
````python
def load_fine_tuned_model(checkpoint_path):
    """
    Load a fine-tuned PEFT model from checkpoint.

    Args:
        checkpoint_path: Path to the checkpoint directory

    Returns:
        tuple: (model, tokenizer, base_model_info)
    """
    print(f"Loading model from checkpoint: {checkpoint_path}")

    # Check if base_model_info.json exists
    base_model_info_path = os.path.join(checkpoint_path, "base_model_info.json")
    if not os.path.exists(base_model_info_path):
        raise FileNotFoundError(f"base_model_info.json not found in {checkpoint_path}")

    # Load base model information
    with open(base_model_info_path, "r") as f:
        base_model_info = json.load(f)

    # Determine base model path
    base_model_path = base_model_info.get("base_model_path", base_model_info["base_model_name"])

    # Load base model
    print(f"Loading base model from: {base_model_path}")
    base_model = AutoModelForCausalLM.from_pretrained(base_model_path)

    # Load PEFT model
    print(f"Loading PEFT adapter from: {checkpoint_path}")
    model = PeftModel.from_pretrained(base_model, checkpoint_path)

    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(checkpoint_path)

    print("Model loaded successfully!")
    return model, tokenizer, base_model_info
````
</augment_code_snippet>

### 实际检查点文件结构

基于项目中的实际输出，一个完整的PEFT检查点目录结构如下：

```
fine_tuned_bloomz_560m/
├── checkpoint_epoch_10/
│   ├── adapter_config.json          # PEFT适配器配置
│   ├── adapter_model.safetensors    # PEFT适配器权重
│   ├── base_model_info.json         # 基础模型信息
│   ├── training_state.json          # 训练状态（JSON部分）
│   ├── training_state.pt            # 训练状态（PyTorch部分）
│   ├── tokenizer.json               # 分词器配置
│   ├── tokenizer_config.json        # 分词器配置
│   └── special_tokens_map.json      # 特殊token映射
├── config.json                      # 基础模型配置
├── generation_config.json           # 生成配置
├── model.safetensors               # 基础模型权重
└── ...
```

### 检查点文件内容分析

#### adapter_model.safetensors 内容

对于P-Tuning方法，权重文件包含虚拟token的嵌入：

```python
# 查看adapter_model.safetensors中的键
import safetensors
from safetensors.torch import load_file

weights = load_file("adapter_model.safetensors")
print("Keys in adapter_model.safetensors:")
for key in weights.keys():
    print(f"  {key}: {weights[key].shape}")

# 输出示例：
# prompt_encoder.default.embedding.weight: torch.Size([20, 1024])
```

#### 训练状态恢复

<augment_code_snippet path="Desktop/HelloWorld/checkpoint.py" mode="EXCERPT">
````python
def load_training_state(checkpoint_dir):
    """加载训练状态用于断点恢复"""
    try:
        # 加载JSON数据
        json_path = os.path.join(checkpoint_dir, "training_state.json")
        with open(json_path, "r") as f:
            json_state = json.load(f)

        # 加载PyTorch tensor数据
        pt_path = os.path.join(checkpoint_dir, "training_state.pt")
        tensor_state = torch.load(pt_path, map_location="cpu")

        # 合并状态
        training_state = {**json_state, **tensor_state}
        return training_state
    except Exception as e:
        print(f"Failed to load training state: {e}")
        return None
````
</augment_code_snippet>

### 模型推理示例

<augment_code_snippet path="Desktop/HelloWorld/load_model.py" mode="EXCERPT">
````python
def generate_response(model, tokenizer, prompt, max_length=512, temperature=0.7):
    """使用微调后的模型生成响应"""
    # 编码输入
    inputs = tokenizer(prompt, return_tensors="pt")

    # 生成响应
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_length=max_length,
            temperature=temperature,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id
        )

    # 解码输出
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)

    # 移除输入部分，只返回生成的内容
    generated_text = response[len(prompt):].strip()
    return generated_text
````
</augment_code_snippet>

### 检查点管理最佳实践

#### 自动清理旧检查点

<augment_code_snippet path="Desktop/HelloWorld/checkpoint.py" mode="EXCERPT">
````python
def cleanup_old_checkpoints(save_dir, keep_latest=True):
    """清理旧的检查点，只保留最新的以节省存储空间"""
    if not keep_latest:
        return

    # 查找所有检查点目录
    checkpoint_dirs = [d for d in os.listdir(save_dir)
                      if d.startswith("checkpoint_epoch_") and
                      os.path.isdir(os.path.join(save_dir, d))]

    if len(checkpoint_dirs) > 1:
        # 按epoch编号排序
        checkpoint_dirs.sort(key=lambda x: int(x.split("_")[-1]))

        # 删除除最新外的所有检查点
        for old_checkpoint in checkpoint_dirs[:-1]:
            old_path = os.path.join(save_dir, old_checkpoint)
            shutil.rmtree(old_path)
            print(f"Removed old checkpoint: {old_path}")
````
</augment_code_snippet>

### 检查点验证

```python
def validate_checkpoint(checkpoint_path):
    """验证检查点的完整性"""
    required_files = [
        "adapter_config.json",
        "adapter_model.safetensors",
        "base_model_info.json"
    ]

    missing_files = []
    for file in required_files:
        if not os.path.exists(os.path.join(checkpoint_path, file)):
            missing_files.append(file)

    if missing_files:
        raise FileNotFoundError(f"Missing required files: {missing_files}")

    # 验证配置文件格式
    try:
        with open(os.path.join(checkpoint_path, "adapter_config.json"), "r") as f:
            config = json.load(f)
            assert "peft_type" in config, "Missing peft_type in adapter_config.json"
    except Exception as e:
        raise ValueError(f"Invalid adapter_config.json: {e}")

    print(f"Checkpoint validation passed: {checkpoint_path}")
```

这些实际的代码示例展示了PEFT检查点格式在真实项目中的应用，包括配置生成、文件保存、加载恢复和验证等完整流程。

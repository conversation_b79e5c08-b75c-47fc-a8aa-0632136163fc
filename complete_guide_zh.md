# 大模型微调与PEFT检查点完整指南

## 目录

1. [项目概述](#项目概述)
2. [微调基础理论](#微调基础理论)
3. [PEFT技术详解](#peft技术详解)
4. [检查点格式规范](#检查点格式规范)
5. [实际代码实现](#实际代码实现)
6. [部署与分发](#部署与分发)
7. [最佳实践](#最佳实践)

## 项目概述

本项目实现了基于DeepSeek和BLOOMZ模型的参数高效微调（PEFT）方案，支持多种微调技术和完整的检查点管理系统。

### 技术栈
- **基础模型**: DeepSeek-Coder-6.7B, BLOOMZ-560M, OPT-350M
- **微调技术**: LoRA, P-Tuning, Prompt Tuning
- **框架**: PyTorch, Transformers, PEFT
- **部署**: HuggingFace Hub, 本地部署

### 项目结构
```
HelloWorld/
├── deepSeek.py              # DeepSeek模型P-Tuning微调
├── checkpoint.py            # 支持断点恢复的完整训练
├── hello_world.py           # LoRA微调示例
├── test.py                 # BLOOMZ P-Tuning微调
├── load_model.py           # 模型加载和推理
├── checkpoint_zh.md        # PEFT检查点格式文档
├── train.md               # 完整微调方案文档
└── fine_tuned_*/          # 微调模型输出目录
```

## 微调基础理论

### 全量微调 vs 参数高效微调

**全量微调（Full Fine-tuning）**
- 更新所有模型参数
- 需要大量GPU内存（通常需要与预训练相同的资源）
- 容易过拟合，特别是小数据集
- 每个任务需要存储完整模型副本

**参数高效微调（PEFT）**
- 只更新少量参数（<1%的原始参数）
- 显著降低内存和计算需求
- 减少过拟合风险
- 多任务可共享基础模型

### 微调方法对比

| 方法 | 参数量 | 内存需求 | 训练速度 | 微调能力 | 适用场景 |
|------|--------|----------|----------|----------|----------|
| 全量微调 | 100% | 高 | 慢 | 强 | 大数据集，充足资源 |
| LoRA | 0.1-1% | 中 | 快 | 中等 | 通用微调任务 |
| P-Tuning | <0.1% | 低 | 很快 | 有限 | 分类，简单生成 |
| Prompt Tuning | <0.01% | 很低 | 很快 | 有限 | 快速原型验证 |

## PEFT技术详解

### LoRA (Low-Rank Adaptation)

#### 数学原理
LoRA将权重更新分解为两个低秩矩阵的乘积：

```
W = W₀ + ΔW = W₀ + BA
其中：B ∈ R^(d×r), A ∈ R^(r×k), r << min(d,k)
参数量：从 d×k 减少到 d×r + r×k
```

#### 实现示例
```python
from peft import LoraConfig, TaskType, get_peft_model

lora_config = LoraConfig(
    r=16,                           # 秩，控制参数量
    target_modules=["q_proj", "v_proj"],  # 目标模块
    task_type=TaskType.CAUSAL_LM,
    lora_alpha=32,                  # 缩放参数
    lora_dropout=0.05               # Dropout率
)

model = get_peft_model(base_model, lora_config)
```

#### 关键参数说明
- **r (rank)**: 低秩矩阵的秩，越大微调能力越强，但参数量增加
- **lora_alpha**: 缩放参数，控制LoRA权重的影响程度
- **target_modules**: 应用LoRA的目标模块，通常选择注意力层
- **lora_dropout**: 防止过拟合的Dropout率

### P-Tuning

#### 技术原理
P-Tuning通过在输入序列前添加可训练的虚拟token实现微调：

```python
from peft import PromptEncoderConfig, get_peft_model

peft_config = PromptEncoderConfig(
    task_type="CAUSAL_LM",
    num_virtual_tokens=20,          # 虚拟token数量
    encoder_hidden_size=128         # 编码器隐藏层大小
)

model = get_peft_model(base_model, peft_config)
```

#### 优势与局限
**优势**:
- 参数量极少（通常<1MB）
- 训练速度极快
- 硬件要求低

**局限**:
- 微调能力有限
- 主要适用于分类任务
- 对复杂生成任务效果不佳

## 检查点格式规范

### PEFT检查点文件结构

一个标准的PEFT检查点包含以下文件：

```
checkpoint_epoch_10/
├── adapter_config.json          # 适配器配置
├── adapter_model.safetensors    # 适配器权重
├── base_model_info.json         # 基础模型信息
├── training_state.json          # 训练状态（基本信息）
├── training_state.pt            # 训练状态（PyTorch对象）
├── tokenizer.json               # 分词器配置
├── tokenizer_config.json        # 分词器配置
└── special_tokens_map.json      # 特殊token映射
```

### adapter_config.json 格式

#### LoRA配置示例
```json
{
  "base_model_name_or_path": "facebook/opt-350m",
  "bias": "none",
  "fan_in_fan_out": false,
  "inference_mode": false,
  "init_lora_weights": true,
  "lora_alpha": 32,
  "lora_dropout": 0.05,
  "peft_type": "LORA",
  "r": 16,
  "target_modules": ["q_proj", "v_proj"],
  "task_type": "CAUSAL_LM"
}
```

#### P-Tuning配置示例
```json
{
  "base_model_name_or_path": "bigscience/bloomz-560m",
  "encoder_hidden_size": 128,
  "num_virtual_tokens": 20,
  "peft_type": "P_TUNING",
  "task_type": "CAUSAL_LM"
}
```

### 权重文件格式

#### LoRA权重命名规则
```
base_model.model.encoder.layer.0.attention.self.query.lora_A.weight
base_model.model.encoder.layer.0.attention.self.query.lora_B.weight
base_model.model.encoder.layer.0.attention.self.value.lora_A.weight
base_model.model.encoder.layer.0.attention.self.value.lora_B.weight
```

#### P-Tuning权重命名规则
```
prompt_encoder.default.embedding.weight  # 形状: [num_virtual_tokens, hidden_size]
```

## 实际代码实现

### 完整训练流程

```python
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import LoraConfig, get_peft_model
from torch.utils.data import DataLoader

# 1. 加载基础模型
model = AutoModelForCausalLM.from_pretrained("facebook/opt-350m")
tokenizer = AutoTokenizer.from_pretrained("facebook/opt-350m")

# 2. 配置PEFT
lora_config = LoraConfig(
    r=16,
    target_modules=["q_proj", "v_proj"],
    task_type="CAUSAL_LM",
    lora_alpha=32,
    lora_dropout=0.05
)

model = get_peft_model(model, lora_config)
model.print_trainable_parameters()

# 3. 准备数据
train_dataloader = DataLoader(train_dataset, batch_size=16, shuffle=True)

# 4. 训练循环
optimizer = torch.optim.AdamW(model.parameters(), lr=3e-4)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.to(device)

for epoch in range(num_epochs):
    model.train()
    for batch in train_dataloader:
        batch = {k: v.to(device) for k, v in batch.items()}
        
        outputs = model(**batch)
        loss = outputs.loss
        
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()
    
    # 5. 保存检查点
    model.save_pretrained(f"./checkpoint_epoch_{epoch+1}")
    tokenizer.save_pretrained(f"./checkpoint_epoch_{epoch+1}")
```

### 断点恢复实现

```python
def save_training_state(checkpoint_dir, epoch, best_eval_loss, optimizer, lr_scheduler):
    """保存完整训练状态"""
    training_state = {
        "epoch": epoch,
        "best_eval_loss": best_eval_loss,
        "optimizer_state_dict": optimizer.state_dict(),
        "lr_scheduler_state_dict": lr_scheduler.state_dict(),
    }
    
    # 保存基本信息
    with open(os.path.join(checkpoint_dir, "training_state.json"), "w") as f:
        json_state = {k: v for k, v in training_state.items() 
                     if k not in ["optimizer_state_dict", "lr_scheduler_state_dict"]}
        json.dump(json_state, f, indent=2)
    
    # 保存PyTorch对象
    torch.save({
        "optimizer_state_dict": training_state["optimizer_state_dict"],
        "lr_scheduler_state_dict": training_state["lr_scheduler_state_dict"],
    }, os.path.join(checkpoint_dir, "training_state.pt"))

def load_training_state(checkpoint_dir):
    """加载训练状态"""
    try:
        with open(os.path.join(checkpoint_dir, "training_state.json"), "r") as f:
            json_state = json.load(f)
        
        tensor_state = torch.load(os.path.join(checkpoint_dir, "training_state.pt"))
        return {**json_state, **tensor_state}
    except Exception as e:
        print(f"Failed to load training state: {e}")
        return None
```

### 模型加载与推理

```python
from peft import PeftModel

def load_fine_tuned_model(checkpoint_path):
    """加载微调后的模型"""
    # 读取基础模型信息
    with open(os.path.join(checkpoint_path, "base_model_info.json"), "r") as f:
        base_model_info = json.load(f)
    
    # 加载基础模型
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_info["base_model_path"]
    )
    
    # 加载PEFT适配器
    model = PeftModel.from_pretrained(base_model, checkpoint_path)
    tokenizer = AutoTokenizer.from_pretrained(checkpoint_path)
    
    return model, tokenizer

def generate_response(model, tokenizer, prompt, max_length=512):
    """生成响应"""
    inputs = tokenizer(prompt, return_tensors="pt")
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_length=max_length,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response[len(prompt):].strip()
```

## 部署与分发

### HuggingFace Hub部署

```python
from huggingface_hub import login

# 登录HuggingFace
login(token="your_hf_token")

# 上传模型
model.push_to_hub("your-org/model-name")
tokenizer.push_to_hub("your-org/model-name")
```

### 私有化部署

```python
# 方案1：保持PEFT结构
model.save_pretrained("./deployment/adapter")
base_model.save_pretrained("./deployment/base_model")

# 方案2：合并权重
merged_model = model.merge_and_unload()
merged_model.save_pretrained("./deployment/merged_model")
```

## 最佳实践

### 1. 参数选择建议

**LoRA参数**:
- r=8-16: 适合大多数任务
- r=32-64: 复杂任务或大幅改变模型行为
- lora_alpha=2*r: 常用的缩放比例
- target_modules: 包含q_proj, v_proj, k_proj, o_proj

**P-Tuning参数**:
- num_virtual_tokens=10-50: 根据任务复杂度调整
- encoder_hidden_size=128-512: 平衡性能和参数量

### 2. 训练策略

```python
# 学习率调度
from transformers import get_linear_schedule_with_warmup

lr_scheduler = get_linear_schedule_with_warmup(
    optimizer=optimizer,
    num_warmup_steps=100,
    num_training_steps=total_steps
)

# 梯度累积
gradient_accumulation_steps = 4
for step, batch in enumerate(dataloader):
    outputs = model(**batch)
    loss = outputs.loss / gradient_accumulation_steps
    loss.backward()
    
    if (step + 1) % gradient_accumulation_steps == 0:
        optimizer.step()
        lr_scheduler.step()
        optimizer.zero_grad()
```

### 3. 监控与评估

```python
# 训练监控
def log_metrics(epoch, train_loss, eval_loss):
    train_ppl = torch.exp(train_loss)
    eval_ppl = torch.exp(eval_loss)
    
    print(f"Epoch {epoch}: Train PPL={train_ppl:.4f}, Eval PPL={eval_ppl:.4f}")
    
    # 保存到文件
    with open("training_log.txt", "a") as f:
        f.write(f"{epoch},{train_loss:.4f},{eval_loss:.4f}\n")

# 早停机制
class EarlyStopping:
    def __init__(self, patience=5, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.best_loss = float('inf')
        self.counter = 0
    
    def __call__(self, val_loss):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            return False
        else:
            self.counter += 1
            return self.counter >= self.patience
```

### 4. 检查点管理

```python
def cleanup_old_checkpoints(save_dir, keep_latest=True):
    """只保留最新的检查点"""
    if not keep_latest:
        return
    
    checkpoints = [d for d in os.listdir(save_dir) 
                  if d.startswith("checkpoint_epoch_")]
    
    if len(checkpoints) > 1:
        checkpoints.sort(key=lambda x: int(x.split("_")[-1]))
        for old_checkpoint in checkpoints[:-1]:
            shutil.rmtree(os.path.join(save_dir, old_checkpoint))
```

## 总结

本指南提供了完整的大模型微调和PEFT检查点管理方案，涵盖了从理论基础到实际实现的全部内容。通过合理选择微调方法、正确配置参数、实施完善的检查点管理，可以高效地完成大模型的定制化微调任务。

关键要点：
1. 根据任务需求选择合适的PEFT方法
2. 正确配置和保存检查点格式
3. 实施完善的训练监控和恢复机制
4. 选择合适的部署和分发策略

#!/usr/bin/env python3
# 验证LoRA配置

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model
import json

# 配置参数
base_model_name = "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"

def verify_lora_config():
    print("🔍 Verifying LoRA configuration...")
    
    try:
        # 加载模型
        print("📥 Loading base model...")
        model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            low_cpu_mem_usage=True,
            trust_remote_code=True
        )
        
        # 创建LoRA配置
        print("⚙️ Creating LoRA configuration...")
        peft_config = LoraConfig(
            task_type="CAUSAL_LM",
            inference_mode=False,
            r=8,
            lora_alpha=16,
            lora_dropout=0.0,
            bias="none",
            target_modules=["k_proj", "down_proj", "q_proj", "v_proj", "up_proj", "gate_proj", "o_proj"],
            init_lora_weights=True,
            use_dora=False,
            use_rslora=False
        )
        
        # 应用LoRA
        print("🎯 Applying LoRA to model...")
        model = get_peft_model(model, peft_config)
        
        # 打印可训练参数
        print("📊 Trainable parameters:")
        model.print_trainable_parameters()
        
        # 验证配置
        print("✅ LoRA configuration verification:")
        print(f"   - Rank (r): {peft_config.r}")
        print(f"   - Alpha: {peft_config.lora_alpha}")
        print(f"   - Dropout: {peft_config.lora_dropout}")
        print(f"   - Bias: {peft_config.bias}")
        print(f"   - Target modules: {peft_config.target_modules}")
        print(f"   - Use DoRA: {peft_config.use_dora}")
        print(f"   - Use RSLoRA: {peft_config.use_rslora}")
        
        # 保存配置到文件以供比较
        config_dict = {
            "task_type": peft_config.task_type,
            "r": peft_config.r,
            "lora_alpha": peft_config.lora_alpha,
            "lora_dropout": peft_config.lora_dropout,
            "bias": peft_config.bias,
            "target_modules": list(peft_config.target_modules),  # 转换为列表
            "init_lora_weights": peft_config.init_lora_weights,
            "use_dora": peft_config.use_dora,
            "use_rslora": peft_config.use_rslora,
            "inference_mode": peft_config.inference_mode
        }
        
        with open("current_lora_config.json", "w") as f:
            json.dump(config_dict, f, indent=2)
        
        print("💾 Current configuration saved to 'current_lora_config.json'")
        print("🎉 LoRA configuration verification completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_lora_config()
    if success:
        print("\n✅ LoRA configuration is correct and ready for training!")
    else:
        print("\n❌ LoRA configuration verification failed!")

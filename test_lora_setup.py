#!/usr/bin/env python3
# 测试LoRA设置

import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model
import json

# 配置参数
base_model_name = "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"

def test_lora_setup():
    print("🔧 Testing LoRA setup...")
    
    try:
        # 测试分词器加载
        print("📝 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(base_model_name)
        if tokenizer.pad_token_id is None:
            tokenizer.pad_token_id = tokenizer.eos_token_id
        print(f"✅ Tokenizer loaded. Vocab size: {tokenizer.vocab_size}")
        
        # 测试数据加载
        print("📊 Testing data loading...")
        with open("PsyDTCorpus_train_mulit_turn_packing.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Data loaded. Sample count: {len(data)}")
        
        # 测试模型加载（使用内存优化）
        print("🤖 Loading base model with memory optimization...")
        model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            low_cpu_mem_usage=True,
            trust_remote_code=True
        )
        print(f"✅ Base model loaded. Parameters: {model.num_parameters():,}")
        
        # 测试LoRA配置
        print("🔗 Creating LoRA configuration...")
        peft_config = LoraConfig(
            task_type="CAUSAL_LM",
            inference_mode=False,
            r=16,
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
        )
        
        # 应用LoRA
        print("🎯 Applying LoRA...")
        model = get_peft_model(model, peft_config)
        model.print_trainable_parameters()
        
        # 启用梯度检查点
        print("💾 Enabling gradient checkpointing...")
        model.gradient_checkpointing_enable()
        
        print("🎉 LoRA setup test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during LoRA setup: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_lora_setup()
    if success:
        print("\n✅ All tests passed! LoRA training setup is ready.")
    else:
        print("\n❌ Tests failed! Please check the configuration.")

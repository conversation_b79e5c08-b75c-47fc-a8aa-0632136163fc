# PEFT Model Training with Checkpoint Recovery

这是一个使用 PEFT (Parameter-Efficient Fine-Tuning) 技术对 BLOOMZ-560M 模型进行微调的完整训练脚本，支持断点恢复和智能checkpoint管理。

## 🚀 功能特性

### 核心功能
- **PEFT微调**: 使用 P-Tuning 技术对大语言模型进行参数高效微调
- **断点恢复**: 支持从任意checkpoint恢复训练，包括模型状态、优化器状态和学习率调度器状态
- **智能checkpoint管理**: 自动清理旧checkpoint，只保留最新的一个以节省存储空间
- **基础模型保存**: 将基础模型直接保存在输出目录根目录，确保模型完整性

### 高级特性
- **自动checkpoint检测**: 如果不指定恢复路径，自动寻找最新的checkpoint
- **完整训练状态保存**: 保存epoch、最佳评估损失、优化器和调度器状态
- **灵活配置**: 通过配置参数控制各种行为
- **设备自适应**: 自动检测并使用可用的计算设备 (MPS/CPU)

## 📋 依赖要求

```bash
pip install torch transformers datasets peft tqdm
```

## 🔧 配置参数

在脚本开头可以修改以下配置：

```python
# 模型配置
base_model_name = "bigscience/bloomz-560m"  # 基础模型名称

# 训练配置
lr = 3e-2                                   # 学习率
num_epochs = 50                             # 训练轮数
checkpoint_interval = 10                    # checkpoint保存间隔

# 恢复配置
resume_from_checkpoint = None               # 指定checkpoint路径，None为自动检测

# 存储配置
save_base_model_copy = True                 # 是否保存基础模型副本
keep_only_latest_checkpoint = True          # 是否只保留最新checkpoint
```

## 📁 输出目录结构

```
fine_tuned_bloomz_560m/
├── config.json                    # 基础模型配置
├── pytorch_model.bin              # 基础模型权重
├── tokenizer.json                 # tokenizer文件
├── tokenizer_config.json          # tokenizer配置
├── special_tokens_map.json        # 特殊token映射
└── checkpoint_epoch_50/           # 最新checkpoint
    ├── adapter_config.json        # PEFT适配器配置
    ├── adapter_model.bin          # PEFT适配器权重
    ├── base_model_info.json       # 基础模型信息
    ├── training_state.json        # 训练状态
    └── tokenizer files...         # tokenizer文件
```

## 🎯 使用方法

### 1. 从头开始训练
```bash
python bloomz-560m.py
```

### 2. 从指定checkpoint恢复
修改脚本中的配置：
```python
resume_from_checkpoint = "./fine_tuned_bloomz_560m/checkpoint_epoch_20"
```

### 3. 自动恢复最新checkpoint
脚本会自动检测并从最新的checkpoint恢复训练（默认行为）

## 📊 训练监控

训练过程中会显示以下信息：
- 每个epoch的训练和验证损失
- 训练和验证困惑度 (Perplexity)
- Checkpoint保存信息
- 最佳模型更新信息

示例输出：
```
epoch=0: train_ppl=45.2341 train_epoch_loss=3.8123 eval_ppl=42.1234 eval_epoch_loss=3.7456
New best evaluation loss: 3.7456, saving checkpoint...
Checkpoint saved to ./fine_tuned_bloomz_560m/checkpoint_epoch_1
```

## 🔄 Checkpoint恢复机制

### 自动恢复流程
1. 检查是否指定了 `resume_from_checkpoint`
2. 如果未指定，自动搜索最新的checkpoint
3. 加载PEFT模型和训练状态
4. 恢复优化器和学习率调度器状态
5. 从上次停止的epoch继续训练

### 保存的状态信息
- **模型状态**: PEFT适配器权重
- **训练进度**: 当前epoch和最佳评估损失
- **优化器状态**: AdamW优化器的内部状态
- **调度器状态**: 学习率调度器的状态

## 💾 存储优化

### 智能Checkpoint管理
- 默认只保留最新的checkpoint，自动删除旧的
- 可通过 `keep_only_latest_checkpoint = False` 保留所有checkpoint
- 大大减少存储空间占用

### 基础模型管理
- 基础模型直接保存在输出目录根目录
- 避免重复下载，支持完全离线使用
- 每个checkpoint包含基础模型路径信息

## 🛠️ 故障排除

### 常见问题

1. **内存不足**
   - 减小 `batch_size`
   - 使用梯度累积

2. **Checkpoint损坏**
   - 脚本会自动回退到从头训练
   - 检查磁盘空间是否充足

3. **设备兼容性**
   - 脚本自动检测MPS/CPU
   - 可手动修改device设置

### 调试模式
设置较小的参数进行测试：
```python
num_epochs = 2
checkpoint_interval = 1
```

## 🔧 模型加载和使用

项目还包含了 `load_model.py` 脚本，用于加载训练好的模型：

```python
# 加载最新的checkpoint
python load_model.py
```

### 加载脚本功能
- 自动检测最新checkpoint
- 加载PEFT模型和基础模型
- 提供文本生成示例
- 支持自定义推理

### 使用示例
```python
from load_model import load_model_from_checkpoint, generate_text

# 加载模型
model, tokenizer, base_model_info = load_model_from_checkpoint("./fine_tuned_bloomz_560m/checkpoint_epoch_50")

# 生成文本
prompt = "Tweet text : Great service! Label : "
generated_text = generate_text(model, tokenizer, prompt)
print(generated_text)
```

## 📝 扩展使用

### 更换数据集
修改数据集加载部分：
```python
ds = load_dataset("your_dataset_name")
```

### 更换基础模型
修改模型配置：
```python
base_model_name = "your_model_name"
```

### 调整PEFT配置
修改PEFT参数：
```python
peft_config = PromptEncoderConfig(
    task_type="CAUSAL_LM",
    num_virtual_tokens=20,
    encoder_hidden_size=128
)
```

## 📊 性能优化建议

### 训练优化
- 使用混合精度训练减少内存使用
- 调整batch_size以充分利用GPU
- 使用梯度累积处理大batch

### 存储优化
- 启用 `keep_only_latest_checkpoint` 节省空间
- 定期清理不需要的中间文件
- 考虑使用模型压缩技术

## 📄 许可证

本项目遵循 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**注意**: 首次运行时会下载基础模型和数据集，请确保网络连接稳定。

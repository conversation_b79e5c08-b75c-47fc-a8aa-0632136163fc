# 导入必要的库
from datasets import Dataset  # 用于创建数据集
from transformers import AutoTokenizer, AutoModelForCausalLM, get_linear_schedule_with_warmup  # Transformers库的核心组件
from torch.utils.data import DataLoader  # PyTorch数据加载器
from transformers import default_data_collator  # 默认数据整理器
from peft import LoraConfig, get_peft_model, PeftModel  # PEFT（参数高效微调）相关组件
from tqdm import tqdm  # 进度条显示
import torch  # PyTorch深度学习框架
import os  # 操作系统接口
import json  # JSON数据处理
import glob  # 文件路径匹配

# 配置参数
base_model_name = "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"  # 基础模型名称

# 数据集文件路径
train_data_path = "PsyDTCorpus_train_mulit_turn_packing.json"  # 训练数据文件路径
test_data_path = "PsyDTCorpus_test_single_turn_split.json"  # 测试数据文件路径

def load_psydtc_data(file_path):
    """
    加载PsyDTC数据集

    Args:
        file_path: JSON文件路径

    Returns:
        处理后的数据列表
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    processed_data = []
    for item in data:
        # 提取对话内容，构建输入输出对
        messages = item['messages']
        tag = item['normalizedTag']  # 心理咨询标签

        # 构建对话历史和目标回复
        conversation_history = []
        for i, message in enumerate(messages):
            if message['role'] == 'system':
                continue  # 跳过系统消息
            elif message['role'] == 'user':
                conversation_history.append(f"用户: {message['content']}")
            elif message['role'] == 'assistant' and i > 0:
                # 构建输入（对话历史）和输出（助手回复）
                input_text = " ".join(conversation_history)
                output_text = message['content']

                processed_data.append({
                    'input_text': input_text,
                    'output_text': output_text,
                    'tag': tag
                })

                # 将助手回复加入对话历史，用于下一轮
                conversation_history.append(f"助手: {message['content']}")

    return processed_data

# 加载数据集
print("Loading PsyDTC dataset...")
train_data = load_psydtc_data(train_data_path)
test_data = load_psydtc_data(test_data_path)

print(f"训练数据样本数: {len(train_data)}")
print(f"测试数据样本数: {len(test_data)}")
print(f"训练数据示例: {train_data[0]}")

# 创建Hugging Face Dataset对象
train_dataset = Dataset.from_list(train_data)
test_dataset = Dataset.from_list(test_data)

# 创建数据集字典
ds = {
    "train": train_dataset,
    "test": test_dataset
}

# 加载分词器
tokenizer = AutoTokenizer.from_pretrained(base_model_name)  # 从预训练模型加载分词器
if tokenizer.pad_token_id is None:  # 如果没有填充token
    tokenizer.pad_token_id = tokenizer.eos_token_id  # 使用结束token作为填充token

# 预处理函数配置
max_length = 512  # 设置输入序列的最大长度，心理咨询对话通常较长

def preprocess_function(examples):
    """
    数据预处理函数
    将PsyDTC对话数据转换为模型可以处理的格式
    """
    batch_size = len(examples["input_text"])  # 获取批次大小

    # 构造输入格式：对话历史 + 特殊分隔符 + 回复
    inputs = []
    targets = []

    for i in range(batch_size):
        input_text = examples["input_text"][i]
        output_text = examples["output_text"][i]

        # 构造完整的训练样本：输入 + 输出
        full_text = f"{input_text}\n助手: {output_text}"
        inputs.append(input_text)
        targets.append(output_text)

    # 对输入和目标进行分词
    model_inputs = tokenizer(
        inputs,
        truncation=True,
        padding=True,
        max_length=max_length,
        return_tensors="pt"
    )

    labels = tokenizer(
        targets,
        truncation=True,
        padding=True,
        max_length=max_length,
        return_tensors="pt"
    )

    # 设置标签，用于计算损失
    model_inputs["labels"] = labels["input_ids"].clone()

    # 将填充位置的标签设置为-100，这样在计算损失时会被忽略
    model_inputs["labels"][model_inputs["labels"] == tokenizer.pad_token_id] = -100

    return model_inputs

# 处理数据集
processed_ds = {}
for split in ["train", "test"]:
    processed_ds[split] = ds[split].map(
        preprocess_function,  # 应用预处理函数
        batched=True,  # 批量处理
        num_proc=1,  # 使用单进程
        remove_columns=ds[split].column_names,  # 移除原始列，只保留处理后的数据
        load_from_cache_file=False,  # 不从缓存文件加载
        desc=f"Running tokenizer on {split} dataset",  # 进度条描述
    )

# 创建数据加载器
train_ds = processed_ds["train"]  # 训练数据集
eval_ds = processed_ds["test"]  # 评估数据集
batch_size = 16  # 批次大小

# 训练数据加载器（启用随机打乱）
train_dataloader = DataLoader(train_ds, shuffle=True, collate_fn=default_data_collator, batch_size=batch_size, pin_memory=True)
# 评估数据加载器（不打乱顺序）
eval_dataloader = DataLoader(eval_ds, collate_fn=default_data_collator, batch_size=batch_size, pin_memory=True)

# 训练配置参数
lr = 1e-4  # 学习率，对于大模型使用较小的学习率
num_epochs = 10  # 训练轮数，心理咨询数据较复杂，适当减少轮数
checkpoint_interval = 2  # 每2个epoch保存一次检查点
resume_from_checkpoint = None  # 设置为检查点路径以恢复训练，None表示从头开始
save_base_model_copy = True  # 是否保存基础模型的副本
keep_only_latest_checkpoint = True  # 是否只保留最新的检查点

# 模型和检查点保存目录
save_dir = "./fine_tuned_deepseek_r1"  # 保存目录路径
os.makedirs(save_dir, exist_ok=True)  # 创建保存目录（如果不存在）

# 辅助函数定义
def find_latest_checkpoint(save_dir):
    """
    在保存目录中查找最新的检查点

    Args:
        save_dir: 保存目录路径

    Returns:
        最新检查点的路径，如果没有找到则返回None
    """
    checkpoint_pattern = os.path.join(save_dir, "checkpoint_epoch_*")  # 检查点文件名模式
    checkpoints = glob.glob(checkpoint_pattern)  # 查找所有匹配的检查点文件
    if not checkpoints:
        return None

    # 提取epoch编号并找到最新的
    epoch_numbers = []
    for checkpoint in checkpoints:
        try:
            epoch_num = int(checkpoint.split("_epoch_")[-1])  # 从文件名中提取epoch编号
            epoch_numbers.append((epoch_num, checkpoint))
        except ValueError:
            continue  # 跳过无法解析的文件名

    if epoch_numbers:
        latest_epoch, latest_checkpoint = max(epoch_numbers, key=lambda x: x[0])  # 找到最大的epoch编号
        return latest_checkpoint
    return None

def save_training_state(checkpoint_dir, epoch, best_eval_loss, optimizer, lr_scheduler):
    """
    保存训练状态，包括优化器和学习率调度器的状态

    Args:
        checkpoint_dir: 检查点保存目录
        epoch: 当前epoch
        best_eval_loss: 最佳评估损失
        optimizer: 优化器
        lr_scheduler: 学习率调度器
    """
    training_state = {
        "epoch": epoch,  # 当前epoch
        "best_eval_loss": best_eval_loss,  # 最佳评估损失
        "optimizer_state_dict": optimizer.state_dict(),  # 优化器状态
        "lr_scheduler_state_dict": lr_scheduler.state_dict(),  # 学习率调度器状态
    }

    state_path = os.path.join(checkpoint_dir, "training_state.json")  # 训练状态文件路径
    torch.save(training_state, state_path)  # 保存训练状态
    print(f"Training state saved to {state_path}")

def save_base_model(base_model_name, save_dir):
    """
    将基础模型直接保存到输出目录

    Args:
        base_model_name: 基础模型名称
        save_dir: 保存目录
    """
    # 加载基础模型并直接保存到save_dir
    base_model = AutoModelForCausalLM.from_pretrained(base_model_name)  # 加载基础模型
    base_tokenizer = AutoTokenizer.from_pretrained(base_model_name)  # 加载对应的分词器

    base_model.save_pretrained(save_dir)  # 保存模型
    base_tokenizer.save_pretrained(save_dir)  # 保存分词器
    print(f"Base model saved to {save_dir}")

def cleanup_old_checkpoints(save_dir, keep_latest=True):
    """
    清理旧的检查点，可选择只保留最新的一个

    Args:
        save_dir: 保存目录
        keep_latest: 是否只保留最新的检查点
    """
    import shutil

    checkpoint_pattern = os.path.join(save_dir, "checkpoint_epoch_*")  # 检查点文件名模式
    checkpoints = glob.glob(checkpoint_pattern)  # 查找所有检查点

    if not checkpoints:
        return  # 如果没有检查点，直接返回

    # 提取epoch编号并排序
    epoch_checkpoints = []
    for checkpoint in checkpoints:
        try:
            epoch_num = int(checkpoint.split("_epoch_")[-1])  # 从文件名提取epoch编号
            epoch_checkpoints.append((epoch_num, checkpoint))
        except ValueError:
            continue  # 跳过无法解析的文件名

    if not epoch_checkpoints:
        return  # 如果没有有效的检查点，直接返回

    # 按epoch编号排序
    epoch_checkpoints.sort(key=lambda x: x[0])

    if keep_latest and len(epoch_checkpoints) > 1:
        # 只保留最新的检查点
        latest_epoch, latest_checkpoint = epoch_checkpoints[-1]  # 获取最新的检查点
        checkpoints_to_remove = epoch_checkpoints[:-1]  # 获取需要删除的检查点

        print(f"Keeping latest checkpoint: {latest_checkpoint}")
        for epoch_num, checkpoint_path in checkpoints_to_remove:
            print(f"Removing old checkpoint: {checkpoint_path}")
            shutil.rmtree(checkpoint_path)  # 删除旧检查点目录
    elif not keep_latest:
        # 删除所有检查点
        for epoch_num, checkpoint_path in epoch_checkpoints:
            print(f"Removing checkpoint: {checkpoint_path}")
            shutil.rmtree(checkpoint_path)  # 删除检查点目录

def load_training_state(checkpoint_dir):
    """
    从检查点加载训练状态

    Args:
        checkpoint_dir: 检查点目录

    Returns:
        训练状态字典，如果不存在则返回None
    """
    state_path = os.path.join(checkpoint_dir, "training_state.json")  # 训练状态文件路径
    if os.path.exists(state_path):
        training_state = torch.load(state_path, map_location="cpu")  # 加载训练状态到CPU
        return training_state
    return None

# 如果需要，保存基础模型副本
if save_base_model_copy:
    save_base_model(base_model_name, save_dir)

# 如果没有指定恢复检查点，则查找现有的检查点
if resume_from_checkpoint is None:
    resume_from_checkpoint = find_latest_checkpoint(save_dir)

# 加载模型
training_state = None
if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
    print(f"Resuming training from checkpoint: {resume_from_checkpoint}")

    # 首先加载基础模型
    base_model = AutoModelForCausalLM.from_pretrained(base_model_name)

    # 从检查点加载PEFT模型
    model = PeftModel.from_pretrained(base_model, resume_from_checkpoint)

    # 加载训练状态
    training_state = load_training_state(resume_from_checkpoint)
    if training_state:
        start_epoch = training_state["epoch"]  # 获取开始的epoch
        best_eval_loss = training_state["best_eval_loss"]  # 获取最佳评估损失
        print(f"Resuming from epoch {start_epoch + 1}, best eval loss: {best_eval_loss:.4f}")
    else:
        start_epoch = 0
        best_eval_loss = float("inf")
        print("Warning: Could not load training state, starting fresh")
else:
    print("Starting training from scratch")
    # 加载模型并应用PEFT配置
    model = AutoModelForCausalLM.from_pretrained(base_model_name)  # 加载基础模型
    # 创建LoRA配置：使用LoRA进行参数高效微调
    peft_config = LoraConfig(
        task_type="CAUSAL_LM",  # 因果语言模型任务
        inference_mode=False,   # 训练模式
        r=16,                   # LoRA的秩，控制适配器的大小
        lora_alpha=32,          # LoRA的缩放参数
        lora_dropout=0.1,       # LoRA层的dropout率
        target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]  # 目标模块
    )
    model = get_peft_model(model, peft_config)  # 应用PEFT配置
    start_epoch = 0  # 从第0个epoch开始
    best_eval_loss = float("inf")  # 初始化最佳评估损失为无穷大

model.print_trainable_parameters()  # 打印可训练参数的数量

# 设置优化器和学习率调度器
optimizer = torch.optim.AdamW(model.parameters(), lr=lr)  # 使用AdamW优化器
lr_scheduler = get_linear_schedule_with_warmup(
    optimizer=optimizer,
    num_warmup_steps=0,  # 预热步数为0
    num_training_steps=(len(train_dataloader) * num_epochs),  # 总训练步数
)

# 如果从检查点恢复，则恢复优化器和调度器状态
if resume_from_checkpoint and training_state:
    optimizer.load_state_dict(training_state["optimizer_state_dict"])  # 恢复优化器状态
    lr_scheduler.load_state_dict(training_state["lr_scheduler_state_dict"])  # 恢复调度器状态
    print("Optimizer and scheduler states restored")

# 训练循环
device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")  # 选择设备（MPS或CPU）
model = model.to(device)  # 将模型移动到指定设备

for epoch in range(start_epoch, num_epochs):
    # 训练阶段
    model.train()  # 设置模型为训练模式
    total_loss = 0  # 初始化总损失

    # 遍历训练数据
    for step, batch in enumerate(tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{num_epochs}")):
        batch = {k: v.to(device) for k, v in batch.items()}  # 将批次数据移动到设备
        outputs = model(**batch)  # 前向传播
        loss = outputs.loss  # 获取损失
        total_loss += loss.detach().float()  # 累加损失
        loss.backward()  # 反向传播
        optimizer.step()  # 更新参数
        lr_scheduler.step()  # 更新学习率
        optimizer.zero_grad()  # 清零梯度

    # 评估阶段
    model.eval()  # 设置模型为评估模式
    eval_loss = 0  # 初始化评估损失
    eval_preds = []  # 存储预测结果

    # 遍历评估数据
    for step, batch in enumerate(tqdm(eval_dataloader, desc="Evaluating")):
        batch = {k: v.to(device) for k, v in batch.items()}  # 将批次数据移动到设备
        with torch.no_grad():  # 禁用梯度计算
            outputs = model(**batch)  # 前向传播
        loss = outputs.loss  # 获取损失
        eval_loss += loss.detach().float()  # 累加评估损失
        # 解码预测结果并添加到列表中
        eval_preds.extend(
            tokenizer.batch_decode(torch.argmax(outputs.logits, -1).detach().cpu().numpy(), skip_special_tokens=True)
        )

    # 计算平均损失和困惑度
    eval_epoch_loss = eval_loss / len(eval_dataloader)  # 评估损失
    eval_ppl = torch.exp(eval_epoch_loss)  # 评估困惑度
    train_epoch_loss = total_loss / len(train_dataloader)  # 训练损失
    train_ppl = torch.exp(train_epoch_loss)  # 训练困惑度
    print(f"{epoch=}: {train_ppl=:.4f} {train_epoch_loss=:.4f} {eval_ppl=:.4f} {eval_epoch_loss=:.4f}")

    # 检查点保存：如果评估损失改善或达到指定间隔则保存模型
    checkpoint_dir = os.path.join(save_dir, f"checkpoint_epoch_{epoch+1}")  # 检查点目录路径
    if eval_epoch_loss < best_eval_loss or (epoch + 1) % checkpoint_interval == 0:
        if eval_epoch_loss < best_eval_loss:
            best_eval_loss = eval_epoch_loss  # 更新最佳评估损失
            print(f"New best evaluation loss: {best_eval_loss:.4f}, saving checkpoint...")

        # 直接保存PEFT模型
        os.makedirs(checkpoint_dir, exist_ok=True)  # 创建检查点目录
        model.save_pretrained(checkpoint_dir)  # 保存模型
        tokenizer.save_pretrained(checkpoint_dir)  # 保存分词器

        # 保存基础模型信息
        base_model_info = {
            "base_model_name": base_model_name,  # 基础模型名称
            "base_model_path": save_dir if save_base_model_copy else base_model_name  # 基础模型路径
        }
        with open(os.path.join(checkpoint_dir, "base_model_info.json"), "w") as f:
            json.dump(base_model_info, f, indent=2)  # 保存基础模型信息到JSON文件

        # 保存训练状态
        save_training_state(checkpoint_dir, epoch, best_eval_loss, optimizer, lr_scheduler)
        print(f"Checkpoint saved to {checkpoint_dir}")

        # 如果需要，清理旧的检查点
        if keep_only_latest_checkpoint:
            cleanup_old_checkpoints(save_dir, keep_latest=True)

print("Training completed!")  # 训练完成提示
print(f"Base model {'copied to' if save_base_model_copy else 'referenced as'}: {save_dir if save_base_model_copy else base_model_name}")

# 根据配置显示检查点信息
if keep_only_latest_checkpoint:
    latest_checkpoint = find_latest_checkpoint(save_dir)  # 查找最新检查点
    if latest_checkpoint:
        print(f"Latest checkpoint available at: {latest_checkpoint}")
    else:
        print("No checkpoints found.")
else:
    print(f"All checkpoints saved in: {save_dir}")
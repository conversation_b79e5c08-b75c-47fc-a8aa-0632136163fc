# 导入必要的库
from datasets import load_dataset  # 用于加载数据集
from transformers import AutoTokenizer, AutoModelForCausalLM, get_linear_schedule_with_warmup  # Transformers库的核心组件
from torch.utils.data import DataLoader  # PyTorch数据加载器
from transformers import default_data_collator  # 默认数据整理器
from peft import PromptEncoderConfig, get_peft_model, PeftModel  # PEFT（参数高效微调）相关组件
from tqdm import tqdm  # 进度条显示
import torch  # PyTorch深度学习框架
import os  # 操作系统接口
import json  # JSON数据处理
import glob  # 文件路径匹配

# 配置参数
base_model_name = "bigscience/bloomz-560m"  # 基础模型名称

# 加载数据集
ds = load_dataset("ought/raft", "twitter_complaints")  # 加载Twitter投诉分类数据集

# 处理数据集标签
classes = [k.replace("_", " ") for k in ds["train"].features["Label"].names]  # 将标签名中的下划线替换为空格
ds = ds.map(
    lambda x: {"text_label": [classes[label] for label in x["Label"]]},  # 将数字标签转换为文本标签
    batched=True,  # 批量处理
    num_proc=1,  # 使用单进程
)
print(ds["train"][0])  # 打印第一个训练样本示例

# 加载分词器
tokenizer = AutoTokenizer.from_pretrained(base_model_name)  # 从预训练模型加载分词器
if tokenizer.pad_token_id is None:  # 如果没有填充token
    tokenizer.pad_token_id = tokenizer.eos_token_id  # 使用结束token作为填充token
target_max_length = max([len(tokenizer(class_label)["input_ids"]) for class_label in classes])  # 计算标签的最大长度
print(f"Target max length: {target_max_length}")

# 预处理函数配置
max_length = 64  # 设置输入序列的最大长度

def preprocess_function(examples, text_column="Tweet text", label_column="text_label"):
    batch_size = len(examples[text_column])
    inputs = [f"{text_column} : {x} Label : " for x in examples[text_column]]
    targets = [str(x) for x in examples[label_column]]
    model_inputs = tokenizer(inputs)
    labels = tokenizer(targets)
    for i in range(batch_size):
        sample_input_ids = model_inputs["input_ids"][i]
        label_input_ids = labels["input_ids"][i]
        model_inputs["input_ids"][i] = [tokenizer.pad_token_id] * (
                max_length - len(sample_input_ids)
        ) + sample_input_ids
        model_inputs["attention_mask"][i] = [0] * (max_length - len(sample_input_ids)) + model_inputs[
            "attention_mask"
        ][i]
        labels["input_ids"][i] = [-100] * (max_length - len(label_input_ids)) + label_input_ids
        model_inputs["input_ids"][i] = torch.tensor(model_inputs["input_ids"][i][:max_length])
        model_inputs["attention_mask"][i] = torch.tensor(model_inputs["attention_mask"][i][:max_length])
        labels["input_ids"][i] = torch.tensor(labels["input_ids"][i][:max_length])
    model_inputs["labels"] = labels["input_ids"]
    return model_inputs

# Process dataset
processed_ds = ds.map(
    preprocess_function,
    batched=True,
    num_proc=1,
    remove_columns=ds["train"].column_names,
    load_from_cache_file=False,
    desc="Running tokenizer on dataset",
)

# Create data loaders
train_ds = processed_ds["train"]
eval_ds = processed_ds["test"]
batch_size = 16

train_dataloader = DataLoader(train_ds, shuffle=True, collate_fn=default_data_collator, batch_size=batch_size, pin_memory=True)
eval_dataloader = DataLoader(eval_ds, collate_fn=default_data_collator, batch_size=batch_size, pin_memory=True)

# Configuration
lr = 3e-2
num_epochs = 50
checkpoint_interval = 10  # Save checkpoint every 10 epochs
resume_from_checkpoint = None  # Set to checkpoint path to resume training
save_base_model_copy = True  # Whether to save a copy of the base model
keep_only_latest_checkpoint = True  # Whether to keep only the latest checkpoint

# Directory to save the model and checkpoints
save_dir = "./fine_tuned_bloomz_560m"
os.makedirs(save_dir, exist_ok=True)

# Helper functions
def find_latest_checkpoint(save_dir):
    """Find the latest checkpoint in the save directory."""
    checkpoint_pattern = os.path.join(save_dir, "checkpoint_epoch_*")
    checkpoints = glob.glob(checkpoint_pattern)
    if not checkpoints:
        return None

    # Extract epoch numbers and find the latest
    epoch_numbers = []
    for checkpoint in checkpoints:
        try:
            epoch_num = int(checkpoint.split("_epoch_")[-1])
            epoch_numbers.append((epoch_num, checkpoint))
        except ValueError:
            continue

    if epoch_numbers:
        latest_epoch, latest_checkpoint = max(epoch_numbers, key=lambda x: x[0])
        return latest_checkpoint
    return None

def save_training_state(checkpoint_dir, epoch, best_eval_loss, optimizer, lr_scheduler):
    """Save training state including optimizer and scheduler states."""
    training_state = {
        "epoch": epoch,
        "best_eval_loss": best_eval_loss,
        "optimizer_state_dict": optimizer.state_dict(),
        "lr_scheduler_state_dict": lr_scheduler.state_dict(),
    }

    state_path = os.path.join(checkpoint_dir, "training_state.json")
    torch.save(training_state, state_path)
    print(f"Training state saved to {state_path}")

def save_base_model(base_model_name, save_dir):
    """Save the base model directly to the output directory."""
    # Load and save base model directly to save_dir
    base_model = AutoModelForCausalLM.from_pretrained(base_model_name)
    base_tokenizer = AutoTokenizer.from_pretrained(base_model_name)

    base_model.save_pretrained(save_dir)
    base_tokenizer.save_pretrained(save_dir)
    print(f"Base model saved to {save_dir}")

def cleanup_old_checkpoints(save_dir, keep_latest=True):
    """Remove old checkpoints, optionally keeping only the latest one."""
    import shutil

    checkpoint_pattern = os.path.join(save_dir, "checkpoint_epoch_*")
    checkpoints = glob.glob(checkpoint_pattern)

    if not checkpoints:
        return

    # Extract epoch numbers and sort
    epoch_checkpoints = []
    for checkpoint in checkpoints:
        try:
            epoch_num = int(checkpoint.split("_epoch_")[-1])
            epoch_checkpoints.append((epoch_num, checkpoint))
        except ValueError:
            continue

    if not epoch_checkpoints:
        return

    # Sort by epoch number
    epoch_checkpoints.sort(key=lambda x: x[0])

    if keep_latest and len(epoch_checkpoints) > 1:
        # Keep only the latest checkpoint
        latest_epoch, latest_checkpoint = epoch_checkpoints[-1]
        checkpoints_to_remove = epoch_checkpoints[:-1]

        print(f"Keeping latest checkpoint: {latest_checkpoint}")
        for epoch_num, checkpoint_path in checkpoints_to_remove:
            print(f"Removing old checkpoint: {checkpoint_path}")
            shutil.rmtree(checkpoint_path)
    elif not keep_latest:
        # Remove all checkpoints
        for epoch_num, checkpoint_path in epoch_checkpoints:
            print(f"Removing checkpoint: {checkpoint_path}")
            shutil.rmtree(checkpoint_path)

def load_training_state(checkpoint_dir):
    """Load training state from checkpoint."""
    state_path = os.path.join(checkpoint_dir, "training_state.json")
    if os.path.exists(state_path):
        training_state = torch.load(state_path, map_location="cpu")
        return training_state
    return None

# Save base model copy if requested
if save_base_model_copy:
    save_base_model(base_model_name, save_dir)

# Check for existing checkpoints if resume_from_checkpoint is not specified
if resume_from_checkpoint is None:
    resume_from_checkpoint = find_latest_checkpoint(save_dir)

# Load model
training_state = None
if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
    print(f"Resuming training from checkpoint: {resume_from_checkpoint}")

    # Load base model first
    base_model = AutoModelForCausalLM.from_pretrained(base_model_name)

    # Load PEFT model from checkpoint
    model = PeftModel.from_pretrained(base_model, resume_from_checkpoint)

    # Load training state
    training_state = load_training_state(resume_from_checkpoint)
    if training_state:
        start_epoch = training_state["epoch"]
        best_eval_loss = training_state["best_eval_loss"]
        print(f"Resuming from epoch {start_epoch + 1}, best eval loss: {best_eval_loss:.4f}")
    else:
        start_epoch = 0
        best_eval_loss = float("inf")
        print("Warning: Could not load training state, starting fresh")
else:
    print("Starting training from scratch")
    # Load model and apply PEFT configuration
    model = AutoModelForCausalLM.from_pretrained(base_model_name)
    peft_config = PromptEncoderConfig(task_type="CAUSAL_LM", num_virtual_tokens=20, encoder_hidden_size=128)
    model = get_peft_model(model, peft_config)
    start_epoch = 0
    best_eval_loss = float("inf")

model.print_trainable_parameters()

# Set up optimizer and scheduler
optimizer = torch.optim.AdamW(model.parameters(), lr=lr)
lr_scheduler = get_linear_schedule_with_warmup(
    optimizer=optimizer,
    num_warmup_steps=0,
    num_training_steps=(len(train_dataloader) * num_epochs),
)

# Restore optimizer and scheduler states if resuming
if resume_from_checkpoint and training_state:
    optimizer.load_state_dict(training_state["optimizer_state_dict"])
    lr_scheduler.load_state_dict(training_state["lr_scheduler_state_dict"])
    print("Optimizer and scheduler states restored")

# Training loop
device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")
model = model.to(device)

for epoch in range(start_epoch, num_epochs):
    model.train()
    total_loss = 0
    for step, batch in enumerate(tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{num_epochs}")):
        batch = {k: v.to(device) for k, v in batch.items()}
        outputs = model(**batch)
        loss = outputs.loss
        total_loss += loss.detach().float()
        loss.backward()
        optimizer.step()
        lr_scheduler.step()
        optimizer.zero_grad()

    model.eval()
    eval_loss = 0
    eval_preds = []
    for step, batch in enumerate(tqdm(eval_dataloader, desc="Evaluating")):
        batch = {k: v.to(device) for k, v in batch.items()}
        with torch.no_grad():
            outputs = model(**batch)
        loss = outputs.loss
        eval_loss += loss.detach().float()
        eval_preds.extend(
            tokenizer.batch_decode(torch.argmax(outputs.logits, -1).detach().cpu().numpy(), skip_special_tokens=True)
        )

    eval_epoch_loss = eval_loss / len(eval_dataloader)
    eval_ppl = torch.exp(eval_epoch_loss)
    train_epoch_loss = total_loss / len(train_dataloader)
    train_ppl = torch.exp(train_epoch_loss)
    print(f"{epoch=}: {train_ppl=:.4f} {train_epoch_loss=:.4f} {eval_ppl=:.4f} {eval_epoch_loss=:.4f}")

    # Checkpointing: Save model if evaluation loss improves or at specified intervals
    checkpoint_dir = os.path.join(save_dir, f"checkpoint_epoch_{epoch+1}")
    if eval_epoch_loss < best_eval_loss or (epoch + 1) % checkpoint_interval == 0:
        if eval_epoch_loss < best_eval_loss:
            best_eval_loss = eval_epoch_loss
            print(f"New best evaluation loss: {best_eval_loss:.4f}, saving checkpoint...")

        # Save PEFT model directly
        os.makedirs(checkpoint_dir, exist_ok=True)
        model.save_pretrained(checkpoint_dir)
        tokenizer.save_pretrained(checkpoint_dir)

        # Save base model info
        base_model_info = {
            "base_model_name": base_model_name,
            "base_model_path": save_dir if save_base_model_copy else base_model_name
        }
        with open(os.path.join(checkpoint_dir, "base_model_info.json"), "w") as f:
            json.dump(base_model_info, f, indent=2)

        # Save training state
        save_training_state(checkpoint_dir, epoch, best_eval_loss, optimizer, lr_scheduler)
        print(f"Checkpoint saved to {checkpoint_dir}")

        # Clean up old checkpoints if requested
        if keep_only_latest_checkpoint:
            cleanup_old_checkpoints(save_dir, keep_latest=True)

print("Training completed!")
print(f"Base model {'copied to' if save_base_model_copy else 'referenced as'}: {save_dir if save_base_model_copy else base_model_name}")
if keep_only_latest_checkpoint:
    latest_checkpoint = find_latest_checkpoint(save_dir)
    if latest_checkpoint:
        print(f"Latest checkpoint available at: {latest_checkpoint}")
    else:
        print("No checkpoints found.")
else:
    print(f"All checkpoints saved in: {save_dir}")